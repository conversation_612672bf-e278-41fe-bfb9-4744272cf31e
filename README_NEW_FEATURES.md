# Cloudflare IP优化器 - 新功能说明

## 🆕 新增功能

### 1. 多端口同时检测 🔌

现在支持同时检测多个端口，不再局限于单个443端口。

**使用方法:**
```bash
# 同时检测443和8443端口
python ip_optimizer.py --port 443,8443

# 检测多个常用端口
python ip_optimizer.py --port 443,8443,2053,2083,2087,2096
```

**参数说明:**
- `--port` 或 `-p`: 支持多个端口，用逗号分隔
- 每个端口都会被独立测试
- 结果会包含所有端口的优质IP

### 2. 无限制IP数量 ♾️

可以设置每个库的最大IP数量为无限制，获取更多IP进行测试。

**使用方法:**
```bash
# 设置为无限制
python ip_optimizer.py --max-ips 0

# 或者设置一个很大的数字
python ip_optimizer.py --max-ips 10000
```

**参数说明:**
- `--max-ips 0`: 表示无限制
- `--max-ips N`: N > 0 表示每个库最多获取N个IP
- 无限制模式会获取库中所有可用IP

### 3. 新增IP库 📚

添加了3个高质量的IP库：

#### BestAli (最佳阿里云IP)
- **来源**: `https://raw.githubusercontent.com/ymyuuu/IPDB/refs/heads/main/BestAli/bestaliv4.txt`
- **特点**: 针对阿里云优化的IP地址
- **库名**: `bestali`

#### BestCF IPv4 (最佳CF IPv4)
- **来源**: `https://raw.githubusercontent.com/ymyuuu/IPDB/refs/heads/main/BestCF/bestcfv4.txt`
- **特点**: 精选的Cloudflare IPv4地址
- **库名**: `bestcfv4`

#### BestCF IPv6 (最佳CF IPv6)
- **来源**: `https://raw.githubusercontent.com/ymyuuu/IPDB/refs/heads/main/BestCF/bestcfv6.txt`
- **特点**: 精选的Cloudflare IPv6地址
- **库名**: `bestcfv6`

### 4. 完整的IP库列表 📋

现在支持的所有IP库（按优先级排序）：

1. **official** - CF官方列表（最高优先级）
2. **cm** - CM整理列表
3. **bestali** - 🆕 最佳阿里云IP
4. **bestcfv4** - 🆕 最佳CF IPv4
5. **bestcfv6** - 🆕 最佳CF IPv6
6. **as13335** - AS13335 CF全段
7. **as209242** - AS209242 CF非官方
8. **proxyip** - 反代IP列表
9. **as24429** - AS24429 Alibaba
10. **as35916** - AS35916
11. **as199524** - AS199524 G-Core

## 🚀 使用示例

### 基础使用
```bash
# 默认设置：CN国家，443端口，每库512个IP
python ip_optimizer.py

# 指定国家和数量
python ip_optimizer.py --country US,JP --count 20
```

### 高级使用
```bash
# 多端口 + 无限制IP + 多国家
python ip_optimizer.py \
  --country US,JP,SG,HK \
  --port 443,8443,2053 \
  --max-ips 0 \
  --count 50 \
  --concurrent 64

# 针对特定场景优化
python ip_optimizer.py \
  --country CN \
  --port 443,8443 \
  --max-ips 1000 \
  --count 30 \
  --output best_cn_ips.txt
```

## 📊 输出格式

结果文件格式保持不变：
```
***************:443#JP 官方优选 678ms
************:8443#US 官方优选 234ms
************:443#SG 反代优选 456ms
```

## 🔧 参数说明

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--country` | `-c` | CN | 目标国家，支持多个用逗号分隔 |
| `--count` | `-n` | 10 | 目标IP数量 |
| `--port` | `-p` | 443 | 🆕 目标端口，支持多个用逗号分隔 |
| `--max-ips` | `-m` | 512 | 🆕 每库最大IP数，0表示无限制 |
| `--concurrent` | | 32 | 并发测试数 |
| `--output` | `-o` | nodes.txt | 输出文件名 |

## 💡 优化建议

1. **多端口测试**: 建议同时测试443和8443端口，提高成功率
2. **无限制模式**: 在网络条件好的情况下使用，可获得更多优质IP
3. **合理并发**: 根据网络环境调整并发数，避免过高导致超时
4. **国家选择**: 可选择多个邻近国家，增加找到优质IP的概率

## 🐛 故障排除

如果遇到问题：
1. 检查网络连接
2. 尝试降低并发数
3. 检查防火墙设置
4. 使用VPN或代理
